import 'dart:async';
import 'dart:ui';

import 'package:connectone/bai_blocs/cubit/post_seller_cubit.dart';
import 'package:connectone/bai_blocs/offers/cubit/offers_cubit.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/not_interested_req.dart';
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_req.dart';
import 'package:connectone/bai_models/seller_offers_res.dart' as sor;
import 'package:connectone/bai_models/seller_offers_res.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/bai_screens/buyer_offers_page.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/filter_popup.dart';
import 'package:connectone/core/bai_widgets/negotiation_card.dart';
import 'package:connectone/core/bai_widgets/quote_summary_widgets.dart';
import 'package:connectone/core/bai_widgets/seller_card.dart';
import 'package:connectone/core/bai_widgets/status_dialog.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/others.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../bai_models/offers_res.dart';
import '../core/bai_widgets/help_info.dart';

class SellerOffersPage extends StatefulWidget {
  const SellerOffersPage({
    Key? key,
    // required this.prchOrdrId,
    required this.item,
    required this.showSummary,
    this.changeStatusDialog,
    this.selectedIndex,
    this.enablePoSummary = false,
    this.buttonText,
    this.deliveryDate,
    required this.categoryId,
    required this.splitName,
    required this.steelMr,
  }) : super(key: key);

  final Content item;
  // final String? prchOrdrId;
  final bool showSummary;
  final int? selectedIndex;
  final ChangeStatusDialog? changeStatusDialog;
  final bool? enablePoSummary;
  final String? buttonText;
  final DateTime? deliveryDate;
  final String? categoryId;
  final String? splitName;
  final bool steelMr;

  @override
  State<SellerOffersPage> createState() => _SellerOffersPageState();
}

class _SellerOffersPageState extends State<SellerOffersPage>
    with AutomaticKeepAliveClientMixin {
  late TutorialCoachMark tutorialCoachMark;

  GlobalKey key1 = GlobalKey();
  GlobalKey key2 = GlobalKey();
  GlobalKey key3 = GlobalKey();

  List<TextEditingController> offerControllers = [];
  List<TextEditingController> finalofferControllers = [];
  List<TextEditingController> commentControllers = [];
  List<TextEditingController> quantityControllers = [];
  List<List<String>> images = [];
  List<List<String>> files = [];
  List<List<String>> audios = [];
  List<List<Uint8List>> imageBytes = [];
  List<List<Uint8List>> fileBytes = [];

  var statuses = [];

  List<OffersReq> offersReq = [];

  var req = ViewOfferReq(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  OffersFilterRes filter = OffersFilterRes(
    variant1OptionGroupName: [],
    variant1OptionNames: [],
    variant2OptionGroupName: [],
    variant2OptionNames: [],
    variant3OptionGroupName: [],
    variant3OptionNames: [],
  );

  @override
  void initState() {
    // print('--------widget.prchOrdrId ${widget.prchOrdrId}');
    // alert(widget.item.prchOrdrId.toString());
    context.read<OffersCubit>().loadSellerOffers(
          widget.item.prchOrdrId.toString(),
          req,
          widget.item.orderGroupId?.toInt() ?? 0,
          widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
          enablePoSummary: widget.enablePoSummary ?? false,
          date: widget.deliveryDate ?? DateTime.now(),
          steelMr: widget.steelMr,
        );
    _selectedIndex = widget.showSummary ? 0 : (widget.selectedIndex ?? 0);
    createTutorial();
    super.initState();
  }

  List<SellerOffer> sellerOffers = [];
  List<sor.AlreadySubmittedQuote> alreadySubmittedQuotes = [];

  @override
  void dispose() {
    for (var controller in offerControllers) {
      controller.dispose();
    }
    for (var controller in commentControllers) {
      controller.dispose();
    }
    for (var controller in quantityControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _showConfirmationDialog(BuildContext context) {
    var style = const TextStyle(
      fontWeight: FontWeight.bold,
      color: Colors.black,
    );
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "Confirm Submission",
            style: style,
          ),
          content: const Text("Do you want to submit this offer?"),
          actions: <Widget>[
            TextButton(
              child: Text(
                "No",
                style: style,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                "Yes",
                style: style,
              ),
              onPressed: () {
                print('--------onPressed---0');
                Navigator.of(context).pop();
                Navigator.of(context).pop();

                onSubmit();
                context.read<OfflineMainBloc>().add(RefreshOfflineStocks());
              },
            ),
          ],
        );
      },
    );
  }

  void onSubmit() {
    offersReq.clear();
    for (int i = 0; i < offerControllers.length; i++) {
      var paths = [...images[i], ...audios[i], ...files[i]];
      var bytes = [...imageBytes[i], ...fileBytes[i]];
      var item = sellerOffers[i];
      var isSubmitted = getAlreadySubmittedQuote(
            item,
            alreadySubmittedQuotes,
          ) !=
          null;
      final offerText = cleanRupeeString(finalofferControllers[i].text);
      if (offerText.isNotEmpty && !isSubmitted) {
        offersReq.add(OffersReq(
          mvtItemId: widget.item.mvtItemId,
          mvtItemName: widget.item.mvtItemName,
          optionGroupId: widget.item.optionGroupId,
          optionGroupName: widget.item.optionGroupName,
          optionName: widget.item.optionName,
          variant1OptionGroupId: item.variant1OptionGroupId,
          variant1OptionGroupName: item.variant1OptionGroupName,
          variant1OptionName: item.variant1OptionName,
          variant2OptionGroupId: item.variant2OptionGroupId,
          variant2OptionGroupName: item.variant2OptionGroupName,
          variant2OptionName: item.variant2OptionName,
          variant3OptionGroupId: item.variant3OptionGroupId,
          variant3OptionGroupName: item.variant3OptionGroupName,
          variant3OptionName: item.variant3OptionName,
          variant1OptionId: item.variant1OptionId,
          variant2OptionId: item.variant2OptionId,
          variant3OptionId: item.variant3OptionId,
          optionId: widget.item.optionId,
          offerPrice: double.tryParse(offerText),
          customerId: double.parse(getCustomerId()),
          vendorId: widget.item.vendorId,
          prchOrdrId: widget.item.prchOrdrId,
          statusCd: widget.item.statusCd,
          remarks: commentControllers[i].text,
          quantity: double.tryParse(quantityControllers[i].text) ?? 0,
          perUnitPrice: (double.tryParse(offerText) ?? 1) /
              (double.tryParse(quantityControllers[i].text) ?? 1),
          paths: paths,
          bytes: bytes,
        ));
      }
    }
    context.read<OffersCubit>().postSellerOffers(
          offersReq,
          widget.item.mvtItemId.toString(),
          gstController.text,
        );
  }

  var quantity = "0";

  bool showQuantity() {
    return !(widget.item.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  bool isSteelReinforcement() {
    return (widget.item.mvtItemName ?? "")
        .toLowerCase()
        .contains("reinforcement");
  }

  String getUnit(SellerOffer item) {
    if (!isSteelReinforcement()) {
      return widget.item.optionName ?? "Unit";
    }

    for (int i = 0; i < 3; i++) {
      final groupName = [
        item.variant1OptionGroupName,
        item.variant2OptionGroupName,
        item.variant3OptionGroupName,
      ][i];

      if ((groupName ?? "").toLowerCase().contains("size")) {
        return [
              item.variant1Unit,
              item.variant2Unit,
              item.variant3Unit,
            ][i] ??
            "Unit";
      }
    }

    return "Unit";
  }

  String getQuantity(SellerOffer item) {
    if (!isSteelReinforcement()) {
      return widget.item.quantity?.toString() ?? "0.0";
    }

    for (int i = 0; i < 3; i++) {
      final groupName = [
        item.variant1OptionGroupName,
        item.variant2OptionGroupName,
        item.variant3OptionGroupName,
      ][i];

      if ((groupName ?? "").toLowerCase().contains("size")) {
        return [
          item.variant1Quantity?.toString() ?? "0.0",
          item.variant2Quantity?.toString() ?? "0.0",
          item.variant3Quantity?.toString() ?? "0.0",
        ][i];
      }
    }

    return "0.0";
  }

  int _selectedIndex = 0;

  // List<AlreadySubmittedQuote> alreadySubmittedQuotes = [];

  List<SummaryResponse> summaryList = [];

  TextEditingController gstController = TextEditingController();

  // Add a list to store offer IDs from successful submissions
  List<int> submittedOfferIds = [];

  // Getter to access submitted offer IDs
  List<int> get getSubmittedOfferIds => submittedOfferIds;

  bool hideProductName() {
    if (_selectedIndex == 0) {
      return true;
    }
    return false;
  }

  @override

  /// Builds the widget tree for the [SellerOffersPage].
  ///
  /// This page displays the summary, quotes and negotiation of a seller
  /// for a given purchase order.
  ///
  /// The page also provides a button to submit a quote.
  Widget build(BuildContext context) {
    print('--------widget.categoryId ${widget.categoryId}');
    super.build(context);
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text("Quote Summary"),
        backgroundColor: AppColors.primaryColor,
        actions: [
          // ReportButton(
          //   prchOrdrId: widget.item.prchOrdrId.toString(),
          // ),
          // IconButton(
          //   onPressed: () {
          //     showTutorial();
          //   },
          //   icon: const Icon(
          //     Icons.help_center,
          //   ),
          // ),
          InfoHelp(
            key: key1,
            onTap: () {
              showTutorial();
            },
          ),
        ],
      ),
      body: BlocListener<PostSellerCubit, PostSellerState>(
        listener: (context, state) {
          print('--------state11 ${state}');
          if (state is PostSellerSuccess) {
            Timer(const Duration(seconds: 1), () {
              // alert("Post seller action completed successfully.");
              context.read<OffersCubit>().loadSellerOffers(
                    widget.item.prchOrdrId.toString(),
                    req,
                    widget.item.orderGroupId?.toInt() ?? 0,
                    widget.categoryId ??
                        widget.item.cappCategoriesId?.toString() ??
                        "",
                    enablePoSummary: widget.enablePoSummary ?? false,
                    date: widget.deliveryDate ?? DateTime.now(),
                    steelMr: widget.steelMr,
                  );
            });
          }
        },
        child: AppLoader(
          child: BlocConsumer<OffersCubit, OffersState>(
            listener: (context, state) {
              print('--------state ${state}');
              if (state is RefreshSellerPage ||
                  state is RefreshSellerQuotesPage) {
                print('--------reload-- ${state}');
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is NotInterestedSuccess) {
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is BuyerOffersSuccess) {
                // Store the offer IDs from the successful submission
                setState(() {
                  submittedOfferIds = state.offerIds;
                });
                alert("Quotes submitted successfully");
                // Optionally print or log the offer IDs
                print("Submitted offer IDs: $submittedOfferIds");
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is BuyerOffersNegotiated) {
                properAlert(state.message);
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is NegotiationAccepted) {
                properAlert(state.message);
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is AcceptNegotiateFailed) {
                properAlert(state.message);
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is DiscardSuccess) {
                alert(state.message);
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
              if (state is BuyerOffersLoaded) {
                setState(() {
                  summaryList = state.summary;
                  filter = state.filter;
                  quantity = widget.item.quantity.toString();
                  if (state.offers.productGst != null) {
                    gstController.text = state.offers.productGst.toString();
                  }
                });
                sellerOffers = state.offers.variants ?? [];
                // Initialize controllers for each offer
                offerControllers = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => TextEditingController(),
                );
                finalofferControllers = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => TextEditingController(),
                );
                commentControllers = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => TextEditingController(),
                );
                quantityControllers = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => TextEditingController()
                    ..text =
                        getQuantity(state.offers.variants![index]).toString(),
                );
                images = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => [],
                );
                imageBytes = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => [],
                );
                files = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => [],
                );
                fileBytes = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => [],
                );
                audios = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => [],
                );

                statuses = List.generate(
                  state.offers.variants?.length ?? 0,
                  (index) => [],
                );
                alreadySubmittedQuotes = state.offers.alreadySubmittedQuote
                        ?.where((element) =>
                            element.negotiationHistory?.isNotEmpty == true)
                        .toList() ??
                    [];
              }
              if (state is BuyerOffersFailed) {
                alert(state.message);
                context.read<OffersCubit>().loadSellerOffers(
                      widget.item.prchOrdrId.toString(),
                      req,
                      widget.item.orderGroupId?.toInt() ?? 0,
                      widget.categoryId ??
                          widget.item.cappCategoriesId?.toString() ??
                          "",
                      enablePoSummary: widget.enablePoSummary ?? false,
                      date: widget.deliveryDate ?? DateTime.now(),
                      steelMr: widget.steelMr,
                    );
              }
            },
            builder: (context, state) {
              (state is BuyerOffersLoading)
                  ? context.loaderOverlay.show()
                  : context.loaderOverlay.hide();
              if (state is BuyerOffersLoaded) {
                var statuses = state.nextStatus?.data ?? [];
                var mrStatus = state.offers.mrStatus ?? "";
                var enableSubmit = ["QUOT", "NEGO", "VASS"].contains(mrStatus);
                return RefreshIndicator(
                  onRefresh: () {
                    context.read<OffersCubit>().loadSellerOffers(
                          widget.item.prchOrdrId.toString(),
                          req,
                          widget.item.orderGroupId?.toInt() ?? 0,
                          widget.categoryId ??
                              widget.item.cappCategoriesId?.toString() ??
                              "",
                          enablePoSummary: widget.enablePoSummary ?? false,
                          date: widget.deliveryDate ?? DateTime.now(),
                          steelMr: widget.steelMr,
                        );
                    return Future.value();
                  },
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 16,
                          left: 16,
                          right: 16,
                          bottom: 4,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: hideProductName()
                                      ? const SizedBox()
                                      : Text(
                                          '${widget.item.mvtItemName}',
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          maxLines: 3,
                                        ),
                                ),
                                const SizedBox(width: 16),
                                hideProductName()
                                    ? const SizedBox()
                                    : IconButton(
                                        constraints: const BoxConstraints(),
                                        padding: EdgeInsets.zero,
                                        icon: const Icon(
                                            Icons.filter_alt_outlined),
                                        onPressed: () {
                                          showDialog(
                                              context: context,
                                              builder: (context) {
                                                return FilterPopup(
                                                  data: filter,
                                                  viewOfferReq: req,
                                                  onApply: (filterReq) {
                                                    req = filterReq;
                                                    context
                                                        .read<OffersCubit>()
                                                        .loadSellerOffers(
                                                          widget.item.prchOrdrId
                                                              .toString(),
                                                          filterReq,
                                                          widget.item
                                                                  .orderGroupId
                                                                  ?.toInt() ??
                                                              0,
                                                          widget.categoryId ??
                                                              widget.item
                                                                  .cappCategoriesId
                                                                  ?.toString() ??
                                                              "",
                                                          enablePoSummary: widget
                                                                  .enablePoSummary ??
                                                              false,
                                                          date: widget
                                                                  .deliveryDate ??
                                                              DateTime.now(),
                                                          steelMr:
                                                              widget.steelMr,
                                                        );
                                                  },
                                                );
                                              });
                                        },
                                      ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            // Text(
                            //   'Buyer: ${state.offers.buyerName}',
                            //   style: const TextStyle(
                            //     fontSize: 14,
                            //     fontWeight: FontWeight.bold,
                            //   ),
                            // ),
                            if (showQuantity())
                              hideProductName()
                                  ? const SizedBox()
                                  : const SizedBox(height: 4),
                            if (showQuantity())
                              hideProductName()
                                  ? const SizedBox()
                                  : Text(
                                      'Quantity: ${widget.item.quantity?.toString()} ${widget.item.optionName ?? ""}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                          ],
                        ),
                      ),
                      // state.offers.variants?.isNotEmpty == true
                      //     ?
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20),
                              ),
                              color: AppColors.white,
                            ),
                            child: Column(
                              children: [
                                // Container(
                                //   height: 64,
                                //   color: AppColors.white,
                                //   child: Container(
                                //     margin:
                                //         const EdgeInsets.fromLTRB(6, 12, 6, 8),
                                //     child: Align(
                                //       alignment: Alignment.centerLeft,
                                //       child: SingleChildScrollView(
                                //         scrollDirection: Axis.horizontal,
                                //         child: Row(
                                //           children: [
                                //             TabItem(
                                //               key: key1,
                                //               text: "Summary",
                                //               index: 0,
                                //               selectedColor:
                                //                   AppColors.primaryColorOld,
                                //               unselectedColor: AppColors.white,
                                //               selectedTextColor: AppColors.white,
                                //               unselectedTextColor: Colors.black,
                                //               selectedIndex: _selectedIndex,
                                //               onTap: (index) {
                                //                 setState(() {
                                //                   _selectedIndex = index;
                                //                 });
                                //               },
                                //               fontSize: 14,
                                //               width: kIsWeb
                                //                   ? 150
                                //                   : ((MediaQuery.of(context)
                                //                               .size
                                //                               .width -
                                //                           20) /
                                //                       3),
                                //             ),
                                //             if (!widget.showSummary)
                                //               TabItem(
                                //                 key: key2,
                                //                 text: "Quotes",
                                //                 index: 1,
                                //                 selectedColor:
                                //                     AppColors.primaryColorOld,
                                //                 unselectedColor: AppColors.white,
                                //                 selectedTextColor:
                                //                     AppColors.white,
                                //                 unselectedTextColor: Colors.black,
                                //                 selectedIndex: _selectedIndex,
                                //                 onTap: (index) {
                                //                   if (!enableSubmit) {
                                //                     properAlertWithOk(
                                //                       "This MR is currently not accepting offers. Do you want to proceed?",
                                //                       () {
                                //                         Navigator.pop(context);
                                //                         setState(() {
                                //                           _selectedIndex = index;
                                //                         });
                                //                       },
                                //                     );
                                //                     return;
                                //                   }
                                //                   setState(() {
                                //                     _selectedIndex = index;
                                //                   });
                                //                 },
                                //                 fontSize: 14,
                                //                 width: kIsWeb
                                //                     ? 150
                                //                     : ((MediaQuery.of(context)
                                //                                 .size
                                //                                 .width -
                                //                             20) /
                                //                         3),
                                //               ),
                                //             if (!widget.showSummary)
                                //               TabItem(
                                //                 key: key3,
                                //                 text: "Negotiation",
                                //                 index: 2,
                                //                 selectedColor:
                                //                     AppColors.primaryColorOld,
                                //                 unselectedColor: AppColors.white,
                                //                 selectedTextColor:
                                //                     AppColors.white,
                                //                 unselectedTextColor: Colors.black,
                                //                 selectedIndex: _selectedIndex,
                                //                 onTap: (index) {
                                //                   setState(() {
                                //                     _selectedIndex = index;
                                //                   });
                                //                 },
                                //                 fontSize: 14,
                                //                 width: kIsWeb
                                //                     ? 150
                                //                     : ((MediaQuery.of(context)
                                //                                 .size
                                //                                 .width -
                                //                             20) /
                                //                         3),
                                //               ),
                                //           ],
                                //         ),
                                //       ),
                                //     ),
                                //   ),
                                // ),
                                Expanded(
                                  child: _selectedIndex == 0
                                      ? summaryList.isEmpty
                                          ? SizedBox(
                                              height: MediaQuery.of(context)
                                                      .size
                                                      .height /
                                                  1.5,
                                              child: Center(
                                                child: Text(
                                                  "No summary found",
                                                  style: largeStyle,
                                                ),
                                              ),
                                            )
                                          : Scrollbar(
                                              child: ListView.separated(
                                                padding: const EdgeInsets.only(
                                                  left: 2,
                                                  right: 2,
                                                  bottom: 40,
                                                ),
                                                itemCount: summaryList.length,
                                                shrinkWrap: true,
                                                itemBuilder: (context, index) {
                                                  return Card(
                                                    elevation: 0,
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(6),
                                                        color:
                                                            //  isApproved ?
                                                            //  Colors.green.shade100 ,
                                                            Colors.white,
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: AppColors
                                                                .primaryColor
                                                                .withOpacity(
                                                                    0.2),
                                                            offset:
                                                                const Offset(
                                                                    0, 2),
                                                            blurRadius: 6,
                                                            spreadRadius: 2,
                                                          ),
                                                        ],
                                                      ),
                                                      margin:
                                                          const EdgeInsets.all(
                                                              1),
                                                      child: Column(
                                                        children: [
                                                          SummaryTable(
                                                            res: summaryList,
                                                            index: index,
                                                            content:
                                                                widget.item,
                                                            statusCd: mrStatus,
                                                            date: widget
                                                                    .deliveryDate ??
                                                                DateTime.now(),
                                                            splitName: widget
                                                                .splitName,
                                                            categoryId: widget
                                                                .categoryId,
                                                            steelMr:
                                                                widget.steelMr,
                                                          ),
                                                          // if (summaryList[index]
                                                          //         .summary
                                                          //         ?.vendorName !=
                                                          //     null)
                                                          for (var status
                                                              in statuses)
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      top: 10),
                                                              child: BaiButton(
                                                                height: 40,
                                                                backgoundColor:
                                                                    getMediumColor(
                                                                        statuses
                                                                            .indexOf(status)),
                                                                onTap: () {
                                                                  var sellerId = summaryList[
                                                                              index]
                                                                          .vendorGroupedProducts?[
                                                                              0]
                                                                          .matchedProducts
                                                                          ?.first
                                                                          .details
                                                                          ?.first
                                                                          .sellerId ??
                                                                      0;
                                                                  var summary =
                                                                      summaryList[
                                                                              index]
                                                                          .summary;
                                                                  var offrIds = OrderStorage
                                                                          .getOrderPairs()
                                                                      .where((e) =>
                                                                          e.sellerId ==
                                                                          sellerId
                                                                              .toString())
                                                                      .map((e) => int.parse(e
                                                                          .prchOrdrId
                                                                          .toString()))
                                                                      .toList();
                                                                  // Previously checked if status is PLCD (placed)
                                                                  // This was used in earlier versions of the code
                                                                  var statusChangeDialog =
                                                                      ChangeStatusDialog(
                                                                    prchOrdrId:
                                                                        offrIds,
                                                                    content:
                                                                        widget
                                                                            .item,
                                                                    vendorId: widget
                                                                            .item
                                                                            .sellerVendorId ??
                                                                        0,
                                                                    enableAudio:
                                                                        status.enableAudio ??
                                                                            false,
                                                                    enablePictures:
                                                                        status.enablePictures ??
                                                                            false,
                                                                    enableComments:
                                                                        status.enableComments ??
                                                                            false,
                                                                    enableLocation:
                                                                        status.enableLocation ??
                                                                            false,
                                                                    reload:
                                                                        () {},
                                                                    statusCd:
                                                                        status.statusCode ??
                                                                            '',
                                                                    statusName:
                                                                        status.buttonText ??
                                                                            '',
                                                                    assignedVendorId:
                                                                        int.tryParse(
                                                                            sellerId.toString()),
                                                                    adminEmail:
                                                                        summary
                                                                            ?.adminEmail,
                                                                    vendorEmail:
                                                                        summary
                                                                            ?.vendorEmail,
                                                                  );
                                                                  // if (showConfirmation) {
                                                                  //   properAlertWithOk(
                                                                  //     "Are you sure you want to place this order?",
                                                                  //     () {
                                                                  //       showDialog(
                                                                  //           context:
                                                                  //               context,
                                                                  //           builder:
                                                                  //               (context) {
                                                                  //             return statusChangeDialog;
                                                                  //           });
                                                                  //       return;
                                                                  //     },
                                                                  //   );
                                                                  // }
                                                                  showDialog(
                                                                      context:
                                                                          context,
                                                                      builder:
                                                                          (context) {
                                                                        return statusChangeDialog;
                                                                      });
                                                                },
                                                                text: status
                                                                        .buttonText
                                                                        ?.toUpperCase() ??
                                                                    "N/A",
                                                              ),
                                                            ),

                                                          state
                                                                      .summary[
                                                                          0]
                                                                      .summary
                                                                      ?.submitOfferYN ==
                                                                  'N'
                                                              ? Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .only(
                                                                          top:
                                                                              8.0),
                                                                  child:
                                                                      Container(
                                                                    width: double
                                                                        .infinity,
                                                                    height: 40,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: AppColors
                                                                          .green,
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              4),
                                                                      border: Border.all(
                                                                          color:
                                                                              AppColors.green),
                                                                    ),
                                                                    child:
                                                                        TextButton(
                                                                      style: const ButtonStyle(
                                                                          // Change text color
                                                                          ),
                                                                      onPressed:
                                                                          () async {
                                                                        // try {
                                                                        // Get all offer IDs from OrderStorage
                                                                        var allOfferIds = OrderStorage.getOrderPairs()
                                                                            .map((e) =>
                                                                                int.parse(e.prchOrdrOffrId))
                                                                            .toList();

                                                                        if (allOfferIds
                                                                            .isEmpty) {
                                                                          alert(
                                                                              "No offers selected. Please select offers before submitting.");
                                                                          return;
                                                                        }

                                                                        // Call the notify-action API
                                                                        await NetworkController()
                                                                            .notifyAction(
                                                                          prchOrdrSplitId:
                                                                              widget.item.prchOrdrSplitId?.toInt() ?? 0,
                                                                          prchOrdrId:
                                                                              widget.item.prchOrdrId?.toInt() ?? 0,
                                                                          statusCd:
                                                                              'QUOT',
                                                                          offerId:
                                                                              allOfferIds,
                                                                        );
                                                                        OrderStorage
                                                                            .clearOrderPairs();
                                                                        context
                                                                            .read<OffersCubit>()
                                                                            .loadSellerOffers(
                                                                              widget.item.prchOrdrId.toString(),
                                                                              req,
                                                                              widget.item.orderGroupId?.toInt() ?? 0,
                                                                              widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
                                                                              enablePoSummary: widget.enablePoSummary ?? false,
                                                                              date: widget.deliveryDate ?? DateTime.now(),
                                                                              steelMr: widget.steelMr,
                                                                            );
                                                                        // context.read<OffersCubit>().loadBuyerOffers(
                                                                        //      widget.item.prchOrdrId.toString(),
                                                                        //       req,
                                                                        //       widget.item.orderGroupId?.toInt() ?? 0,
                                                                        //       widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
                                                                        //       enablePoSummary: widget.enablePoSummary ?? false,
                                                                        //       date: widget.deliveryDate ?? DateTime.now(),
                                                                        //       steelMr: widget.steelMr,
                                                                        //     );
                                                                        // context.read<OfflineMainBloc>().add(RefreshOfflineStocks());

                                                                        alert(
                                                                            "Submit action completed successfully!");

                                                                        // Refresh the page after successful submission
                                                                        // context.read<OffersCubit>().loadBuyerOffers(
                                                                        //   widget.item.prchOrdrId.toString(),
                                                                        //   req,
                                                                        //   widget.item.orderGroupId?.toInt() ?? 0,
                                                                        //   widget.categoryId ?? widget.item.cappCategoriesId?.toString() ?? "",
                                                                        //   enablePoSummary: widget.enablePoSummary ?? false,
                                                                        //   date: widget.deliveryDate ?? DateTime.now(),
                                                                        //   steelMr: widget.steelMr,
                                                                        // );
                                                                        // } catch (e) {
                                                                        //   alert("Error submitting: ${e.toString()}");
                                                                        // }
                                                                      },
                                                                      child:
                                                                          Text(
                                                                        'Submit',
                                                                        style:
                                                                            const TextStyle(
                                                                          color:
                                                                              Colors.white,
                                                                          fontSize:
                                                                              16,
                                                                          fontWeight:
                                                                              FontWeight.bold,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                )
                                                              : SizedBox(),
                                                          const SizedBox(
                                                              height: 10),
                                                          BaiButton(
                                                            height: 40,
                                                            onTap: () {
                                                              var sellerId = summaryList[
                                                                          index]
                                                                      .vendorGroupedProducts?[
                                                                          0]
                                                                      .matchedProducts
                                                                      ?.first
                                                                      .details
                                                                      ?.first
                                                                      .sellerId ??
                                                                  0;
                                                              var prchOrdrIds = OrderStorage
                                                                      .getOrderPairs()
                                                                  .where((e) =>
                                                                      e.sellerId ==
                                                                      sellerId
                                                                          .toString())
                                                                  .map((e) => int.parse(e
                                                                      .prchOrdrId
                                                                      .toString()))
                                                                  .toList();
                                                              var req = NotInterestedReq(
                                                                  prchOrdrIds:
                                                                      prchOrdrIds,
                                                                  vendorId:
                                                                      int.tryParse(
                                                                              getVendorId()) ??
                                                                          0,
                                                                  statusCd:
                                                                      mrStatus,
                                                                  orderGrpId: widget
                                                                          .item
                                                                          .orderGroupId
                                                                          ?.toInt() ??
                                                                      0);
                                                              context
                                                                  .read<
                                                                      OffersCubit>()
                                                                  .notInterested(
                                                                      req);
                                                            },
                                                            text:
                                                                "NOT INTERESTED",
                                                            backgoundColor:
                                                                AppColors
                                                                    .darkRed,
                                                            textColor:
                                                                Colors.white,
                                                          )
                                                          // if (widget.changeStatusDialog != null)
                                                          //   const SizedBox(height: 16),
                                                          // if (widget.changeStatusDialog != null)
                                                          //   BaiButton(
                                                          //     height: 40,
                                                          //     backgoundColor: AppColors.green,
                                                          //     onTap: () {
                                                          //       var sellerId = summaryList[index]
                                                          //               .vendorGroupedProducts?[0]
                                                          //               .matchedProducts
                                                          //               ?.first
                                                          //               .details
                                                          //               ?.first
                                                          //               .sellerId ??
                                                          //           0;
                                                          //       var offrIds = OrderStorage.getOrderPairs()
                                                          //           .where((e) => e.sellerId == sellerId.toString())
                                                          //           .map((e) => int.parse(e.prchOrdrId.toString()))
                                                          //           .toList();

                                                          //       widget.changeStatusDialog?.prchOrdrId = offrIds;
                                                          //       widget.changeStatusDialog?.prchOrdrId = offrIds;
                                                          //       widget.changeStatusDialog?.assignedVendorId =
                                                          //           int.tryParse(sellerId.toString());

                                                          //       showDialog(
                                                          //           context: context,
                                                          //           builder: (context) {
                                                          //             return widget.changeStatusDialog!;
                                                          //           });
                                                          //     },
                                                          //     text: widget.changeStatusDialog?.statusName.toUpperCase() ??
                                                          //         "CHANGE STATUS",
                                                          //   ),
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                },
                                                separatorBuilder:
                                                    (context, index) {
                                                  return Column(
                                                    children: [
                                                      const SizedBox(
                                                          height: 16),
                                                      Row(
                                                        children: [
                                                          const SizedBox(
                                                              width: 6),
                                                          Text(
                                                            "End of Summary ${index + 1}",
                                                            style:
                                                                const TextStyle(
                                                              fontSize: 12,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color:
                                                                  Colors.black,
                                                            ),
                                                          ),
                                                          const Expanded(
                                                            child: Divider(
                                                              height: 20,
                                                              color: Colors
                                                                  .black54,
                                                              thickness: 2,
                                                              indent: 8,
                                                              endIndent: 6,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                          height: 20),
                                                    ],
                                                  );
                                                },
                                              ),
                                            )
                                      : _selectedIndex == 1
                                          ? state.offers.variants?.isEmpty ==
                                                  true
                                              ? SizedBox(
                                                  height: MediaQuery.of(context)
                                                          .size
                                                          .height /
                                                      1.5,
                                                  child: Center(
                                                    child: Text(
                                                      "No quotes found",
                                                      style: largeStyle,
                                                    ),
                                                  ),
                                                )
                                              : ListView.separated(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 2,
                                                    vertical: 1,
                                                  ),
                                                  itemBuilder:
                                                      (BuildContext context,
                                                          int index) {
                                                    var item = state.offers
                                                        .variants![index];
                                                    return SellerCard(
                                                      key: ValueKey(index),
                                                      finalofferController:
                                                          finalofferControllers[
                                                              index],
                                                      number: index + 1,
                                                      offer: item,
                                                      item: widget.item,
                                                      offerController:
                                                          offerControllers[
                                                              index],
                                                      commentController:
                                                          commentControllers[
                                                              index],
                                                      quantityController:
                                                          quantityControllers[
                                                              index],
                                                      showQuantity:
                                                          showQuantity(),
                                                      quantity:
                                                          getQuantity(item),
                                                      unit: getUnit(item),
                                                      onImagesUpdated:
                                                          (images) {
                                                        setState(() {
                                                          this.images[index] =
                                                              images;
                                                        });
                                                      },
                                                      onFilesUpdated: (files) {
                                                        setState(() {
                                                          this.files[index] =
                                                              files;
                                                        });
                                                      },
                                                      onAudiosUpdated:
                                                          (audios) {
                                                        setState(() {
                                                          this.audios[index] =
                                                              audios;
                                                        });
                                                      },
                                                      submittedQuote:
                                                          getAlreadySubmittedQuote(
                                                              item,
                                                              state.offers
                                                                      .alreadySubmittedQuote ??
                                                                  []),
                                                      previousQuote:
                                                          getPreviousQuote(
                                                              item,
                                                              state.offers
                                                                      .previousQuotes ??
                                                                  []),
                                                      images: images[index],
                                                      files: files[index],
                                                      audios: audios[index],
                                                      onSubmit: () {
                                                        _showConfirmationDialog(
                                                            context);
                                                      },
                                                      imageBytes:
                                                          imageBytes[index],
                                                      fileBytes:
                                                          fileBytes[index],
                                                      onImageBytesUpdated:
                                                          (bytes) {
                                                        setState(() {
                                                          imageBytes[index] =
                                                              bytes;
                                                        });
                                                      },
                                                      onFileBytesUpdated:
                                                          (bytes) {
                                                        setState(() {
                                                          fileBytes[index] =
                                                              bytes;
                                                        });
                                                      },
                                                    );
                                                  },
                                                  separatorBuilder:
                                                      (BuildContext context,
                                                          int index) {
                                                    return const SizedBox(
                                                        height: 12);
                                                  },
                                                  itemCount: state.offers
                                                          .variants?.length ??
                                                      0,
                                                )
                                          : alreadySubmittedQuotes.isNotEmpty
                                              ? ListView.separated(
                                                  // padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                                                  itemCount:
                                                      alreadySubmittedQuotes
                                                          .length,
                                                  itemBuilder:
                                                      (context, index) {
                                                    var item =
                                                        convertAlreadySubmittedToOffer(
                                                            alreadySubmittedQuotes[
                                                                index]);

                                                    return NegotiationCard(
                                                      offerDetails: item,
                                                      onAccept:
                                                          (Offer offerDetails) {
                                                        // context
                                                        //     .read<
                                                        //         OffersCubit>()
                                                        //     .acceptOffer(
                                                        //         offerDetails
                                                        //             .id
                                                        //             .toString());
                                                      },
                                                      onNegotiate:
                                                          (Offer offerDetails) {
                                                        // showDialog(
                                                        //   context: context,
                                                        //   builder:
                                                        //       (context) {
                                                        //     return NegotiateDialog(
                                                        //       offer:
                                                        //           offerDetails,
                                                        //       unit: extractUnit(
                                                        //           offerDetails),
                                                        //     );
                                                        //   },
                                                        // );
                                                      },
                                                      showQuantity:
                                                          showQuantity(),
                                                    );
                                                  },
                                                  separatorBuilder:
                                                      (context, index) {
                                                    return const SizedBox(
                                                        height: 12);
                                                  },
                                                )
                                              : SizedBox(
                                                  height: MediaQuery.of(context)
                                                          .size
                                                          .height /
                                                      1.5,
                                                  child: Center(
                                                      child: Text(
                                                    "No negotiations found",
                                                    style: largeStyle,
                                                  )),
                                                ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      if (showSubmitButton() && enableSubmit)
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              if (state.offers.isEnableGst ?? false)
                                Expanded(
                                  flex: 1,
                                  child: TextField(
                                    controller: gstController,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    decoration: const InputDecoration(
                                      hintText: 'GST',
                                      hintStyle: TextStyle(
                                          fontWeight: FontWeight.bold),
                                      isDense: true,
                                      labelText: "GST",
                                      floatingLabelAlignment:
                                          FloatingLabelAlignment.center,
                                      suffix: Text("%"),
                                      labelStyle: TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                              if (state.offers.isEnableGst ?? false)
                                const SizedBox(width: 8),
                              // IconButton(
                              //   icon: const Icon(
                              //     Icons.delete,
                              //     color: Colors.black,
                              //     size: 30,
                              //   ),
                              //   onPressed: () {},
                              // ),
                              Expanded(
                                flex: 2,
                                child: BaiButton(
                                  onTap: () {
                                    if (state.offers.isEnableGst ?? false) {
                                      if (gstController.text.isEmpty) {
                                        alert("Please input GST");
                                        return;
                                      }
                                    }

                                    _showConfirmationDialog(context);
                                  },
                                  text: "SAVE",
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                );
              }
              return const SizedBox();
            },
          ),
        ),
      ),
    );
  }

  bool showSubmitButton() {
    return _selectedIndex == 1;
  }

  AlreadySubmittedQuote? getAlreadySubmittedQuote(
    SellerOffer offer,
    List<AlreadySubmittedQuote> prevQuotes,
  ) {
    for (var quote in prevQuotes) {
      if (quote.variant1OptionId == offer.variant1OptionId &&
          quote.variant2OptionId == offer.variant2OptionId &&
          quote.variant3OptionId == offer.variant3OptionId) {
        return quote;
      }
    }
    return null;
  }

  PreviousQuote? getPreviousQuote(
    SellerOffer offer,
    List<PreviousQuote> prevQuotes,
  ) {
    for (var quote in prevQuotes) {
      if (quote.variant1OptionId == offer.variant1OptionId &&
          quote.variant2OptionId == offer.variant2OptionId &&
          quote.variant3OptionId == offer.variant3OptionId) {
        return quote;
      }
    }
    return null;
  }

  void showTutorial() {
    tutorialCoachMark.show(context: context);
  }

  void createTutorial() {
    tutorialCoachMark = TutorialCoachMark(
      targets: _createTargets(),
      colorShadow: AppColors.primaryColor,
      textSkip: "SKIP",
      paddingFocus: 10,
      opacityShadow: 0.5,
      imageFilter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
      onSkip: () {
        return true;
      },
    );
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "key1",
        keyTarget: key1,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SizedBox(height: 64),
                  Text(
                    "Tap here to view the Quote Summary.\n\nYou will see the quotes along with their prices and quantities.\n\nAdditionally, the summary will display the Subtotal, Discount, GST, and more.",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "summary_table",
        keyTarget: key2,
        alignSkip: Alignment.bottomCenter,
        enableOverlayTab: true,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    "The Summary Table provides a detailed breakdown of your quotes:\n\n"
                    "• Shows buyer/vendor information and site details\n"
                    "• Displays MR number, quotation details and dates\n"
                    "• Lists all products with their quantities and rates\n"
                    "• Calculates subtotals, GST, transportation charges\n"
                    "• Highlights lowest rate combinations in orange\n"
                    "• Allows editing quotes by tapping on prices\n"
                    "• Shows discarded items in red background\n"
                    "• Accepted quotes are highlighted in green",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
    return targets;
  }

  @override
  bool get wantKeepAlive => true;
}
